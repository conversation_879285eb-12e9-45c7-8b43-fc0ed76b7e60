class ProgressNotifier {
  constructor() {
    this.notyf = new Notyf({
      duration: 1500,
      dismissible: false,
      ripple: false,
      position: { x: 'right', y: 'bottom' },
      types: [
        {
          type: 'progress',
          background: 'orange',
          icon: false
        }
      ]
    });
    this.last = new Map();
  }

  update(title, completed, total) {
    if (total < 10485760) {
      return; // 小于10M的文件不显示进度
    }
    const percent = Math.max(1, Math.trunc((10485760 / total) * 100));
    const progress = Math.trunc((completed / total) * 100);
    let last = this.last[title] || 0;
    if (progress % percent === 0 && progress > last) {
      this.last[title] = progress;
      this.notyf.dismissAll();
      this.notyf.open({ type: 'progress', message: `${title}: ${progress} %` });
    }
  }
}

// 并发下载多个文件
async function download(urls) {
  async function downloadFile(url) {
    const notifier = new ProgressNotifier();
    // 从URL中提取并解码文件名
    let filename = url.split('?')[0].split('/').pop();
    try {
      filename = decodeURIComponent(filename);
    } catch (error) {
      console.error('文件名解码失败:', error);
    }

    try {
      const downloadResponse = await fetch(url);
      const totalSize = parseInt(downloadResponse.headers.get('Content-Length'), 10); // 获取文件大小
      if (!downloadResponse.ok && !downloadResponse.status === 206) {
        throw new Error('下载失败');
      }

      // 创建读取流和进度跟踪
      const reader = downloadResponse.body.getReader();
      let receivedLength = 0;
      const chunks = [];

      // 读取数据流
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
        receivedLength += value.length;
        notifier.update(filename, receivedLength, totalSize);
      }

      // 合并数据块并创建blob
      const blob = new Blob(chunks);
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      notyf.success(`下载完成：${filename}`);
    } catch (error) {
      console.error('下载失败:', error);
      notyf.error(`文件 ${filename} 下载失败`);
    }
  }

  // 并发下载所有文件
  try {
    notyf.success('<div class="center b-900">文件下载中</div><div class="sm">请不要关闭当前页面</div>');
    await Promise.all(urls.map((url) => downloadFile(url)));
  } catch (error) {
    console.error('下载失败:', error);
    notyf.error('下载失败');
  }
}
