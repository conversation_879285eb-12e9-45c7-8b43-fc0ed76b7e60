<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    {% if not cfg.DEBUG %}
      <!-- Google tag (gtag.js) -->
      <script async
              src="https://www.googletagmanager.com/gtag/js?id=G-B92W2YQZDL"></script>
      <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
          dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'G-B92W2YQZDL');
      </script>
    {% endif %}

    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="max-image-preview:large" />

    {% block seo %}
      <title>次元画册｜免费下载4K游戏动漫壁纸及Cosplay资源</title>
      <meta name="description"
            content="「次元画册」专注分享全网人气画师作品及Cosplay资源。免费下载4K游戏美图、动漫壁纸。二次元爱好者必藏！" />
      <meta name="keywords" content="游戏插画, 动漫壁纸, Cosplay资源, 免费下载" />
      <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "次元画册｜免费下载4K游戏动漫壁纸及Cosplay资源",
          "description": "「次元画册」专注分享全网人气画师作品及Cosplay资源。免费下载4K游戏美图、动漫壁纸。二次元爱好者必藏！",
          "url": "https://www.{{cfg.DOMAIN}}{{ request.path }}",
          "keywords": "游戏插画, 动漫壁纸, Cosplay资源, 免费下载",
          "publisher": {
            "@type": "Organization",
            "name": "次元画册",
            "url": "https://www.{{cfg.DOMAIN}}",
            "logo": {
              "@type": "ImageObject",
              "url": "{{ static('img/favicon.svg') }}",
              "width": 512,
              "height": 512
            }
          }
        }
      </script>
    {% endblock seo %}

    <title>次元画册</title>
    <link rel="icon"
          type="image/svg+xml"
          href="{{ static('img', 'favicon.svg') }}" />
    <link type="text/css"
          rel="stylesheet"
          href="https://cdn.staticfile.net/pure/3.0.0/pure.min.css" />
    <link type="text/css"
          rel="stylesheet"
          href="https://cdn.staticfile.net/pure/3.0.0/grids-responsive-min.css" />
    <link type="text/css"
          rel="stylesheet"
          href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" />
    <link type="text/css"
          rel="stylesheet"
          href="https://cdn.bootcdn.net/ajax/libs/notyf/3.10.0/notyf.min.css" />
    <link type="text/css"
          rel="stylesheet"
          href="{{ static('css', 'styles.css') }}" />

    <meta name="google-site-verification"
          content="LigtQJ8Nb-sj3mgNdHwSQD2qN1Mh0UoGW4zLFShfOgU" />

    {% block head %}
    {% endblock head %}

    {% if not cfg.DEBUG %}<script type="text/javascript">document.domain = 'pixcc.net';</script>{% endif %}
  </head>

  <body>
    <header>
      <!-- PC 顶部导航栏 -->
      <div class="nav-menu pure-menu pure-menu-horizontal pure-menu-fixed">
        <nav>
          <ul class="pure-menu-list">
            <!-- Logo -->
            <li class="pure-menu-heading">
              <a href="/">
                <img class="nav-logo"
                     src="{{ static('img', 'logo.svg') }}"
                     alt="次元画册" />
              </a>
            </li>
            <!-- NavBar -->
            <li class="pure-menu-item">
              <a href="/w/home/" class="pure-menu-link menu-left"><i class="fa-solid fa-house mgr-2-5"></i>首页</a>
            </li>
            <li class="pure-menu-item">
              <a href="/w/illust/" class="pure-menu-link menu-left"><i class="fa-solid fa-image mgr-2-5"></i>插画</a>
            </li>
            <li class="pure-menu-item">
              <a href="/w/artist/" class="pure-menu-link menu-left"><i class="fa-solid fa-palette mgr-2-5"></i>画师</a>
            </li>
            <li class="pure-menu-item">
              <a href="/w/album/" class="pure-menu-link menu-left"><i class="fa-solid fa-file-zipper mgr-2-5"></i>专辑</a>
            </li>
            <li class="pure-menu-item">
              <a href="/w/purchase/" class="pure-menu-link menu-left"><i class="fa-solid fa-crown fa-beat fg-golden mgr-2-5"></i>会员</a>
            </li>
          </ul>
          <ul class="pure-menu-list">
            <!-- 搜索框 -->
            <li id="searchContainer" class="pure-menu-item">
              <form id="searchForm"
                    class="pure-form"
                    action="/w/search"
                    method="get">
                <input type="search"
                       class="pure-input-rounded search-input"
                       placeholder="🔍 搜索美图"
                       name="kw"
                       value="{{ kw }}" />
                <input type="hidden"
                       name="stype"
                       value="{%- if stype -%}{{ stype }}{%- else -%}illust{%- endif -%}" />
              </form>
              <div id="hot-search-results" class="hot-search-dropdown"></div>
            </li>
            {% if user %}
              <!-- 用户菜单 -->
              <li class="pure-menu-item pure-menu-has-children pure-menu-allow-hover">
                <a href="#" class="pure-menu-link menu-right">
                  <i class="fa-solid fa-user mgr-2-5"></i><span id="navUserName">{{ user.name }}</span>
                </a>
                <!-- 子菜单 -->
                <ul class="pure-menu-children menu-child-container"
                    role="menu"
                    aria-hidden="true">
                  <li class="pure-menu-item">
                    <a href="#"
                       onclick="javascript:fetchAPI('GET', '/w/user/checkin/')"
                       class="pure-menu-link rd-10"><i class="fa-solid fa-check-to-slot fg-rose mgr-2-5"></i>今日打卡</a>
                  </li>
                  <li class="pure-menu-item">
                    <a href="/w/user/profile/" class="pure-menu-link rd-10"><i class="fa-solid fa-address-card fg-blue mgr-2-5"></i>个人资料</a>
                  </li>
                  <li class="pure-menu-item center">
                    <a href="/w/user/followed/" class="pure-menu-link rd-10"><i class="fa-solid fa-palette fg-lightgreen mgr-2-5"></i>我的关注</a>
                  </li>
                  <li class="pure-menu-item center">
                    <a href="/w/user/favorited/" class="pure-menu-link rd-10"><i class="fa-solid fa-star fg-golden mgr-2-5"></i>我的收藏</a>
                  </li>
                  <li class="pure-menu-item center">
                    <a href="/w/user/illusts/" class="pure-menu-link rd-10"><i class="fa-solid fa-image fg-pink mgr-2-5"></i>已购插画</a>
                  </li>
                  <li class="pure-menu-item center">
                    <a href="/w/user/albums/" class="pure-menu-link rd-10"><i class="fa-solid fa-file-zipper fg-purple mgr-2-5"></i>已购专辑</a>
                  </li>
                  <li class="pure-menu-item">
                    <a href="/w/user/logout/" class="pure-menu-link rd-10"><i class="fa-solid fa-right-from-bracket fg-brown mgr-2-5"></i>退出登录</a>
                  </li>
                </ul>
              </li>
            {% else %}
              <!-- 登录按钮 -->
              <li class="pure-menu-item">
                <a href="/w/user/login/"
                   class="pure-menu-link menu-right"
                   title="登录">
                  <i class="fa-solid fa-user-plus fg-emp"></i>
                </a>
              </li>
            {% endif %}
          </ul>
        </nav>
      </div>

      <!-- 移动端底部导航栏 -->
      <nav id="mobileNav" class="mobile-nav">
        <a href="/w/home/" class="nav-item"><i class="fa-solid fa-house"></i><span>首页</span></a>
        <a href="/w/illust/" class="nav-item"><i class="fa-solid fa-image"></i>插画</a>
        <a href="/w/artist/" class="nav-item"><i class="fa-solid fa-palette"></i><span>画师</span></a>
        <a href="/w/album/" class="nav-item"><i class="fa-solid fa-file-zipper"></i><span>专辑</span></a>
        <a href="/w/purchase/" class="nav-item"><i class="fa-solid fa-crown fa-beat fg-golden"></i><span>会员</span></a>
      </nav>
    </header>

    <!-- 页面主体 -->
    <main>
      {% if msg %}
        <div id="msg" class="pure-g">
          <span class="msg notice"><i class="fa-regular fa-lightbulb mgr-2-5"></i>{{ msg | safe }}</span>
        </div>
      {% elif err %}
        <div id="msg" class="pure-g">
          <span class="msg error"><i class="fa-solid fa-circle-exclamation mgr-2-5"></i>{{ err | safe }}</span>
        </div>
      {% endif %}
      <div class="container pure-g">
        <!-- 公告栏 -->
        {% if notices %}
          <div class="notice-marquee-wrapper">
            <div class="notice-marquee">
              {% for notice in notices %}
                <span class="mgr-w-1-4 fg-{{ loop.cycle('blue', 'pink', 'purple') }}">{{ notice|safe }}</span>
              {% endfor %}
            </div>
          </div>
          <script>
            document.addEventListener('DOMContentLoaded', function() {
              var marquee = document.querySelector('.notice-marquee');
              var wrapper = document.querySelector('.notice-marquee-wrapper');
              if (marquee && wrapper) {
                // 速度：每秒滚动 80 像素，可自行调整
                var speed = 80; // px/s
                // 获取内容宽度和容器宽度
                var contentWidth = marquee.scrollWidth;
                var wrapperWidth = wrapper.offsetWidth;
                // 滚动总距离 = 内容宽度 + 容器宽度
                var distance = contentWidth + wrapperWidth;
                // 动画时长 = 距离 / 速度
                var duration = distance / speed;

                // 设置动画
                marquee.style.animation = `notice-marquee-scroll ${duration}s linear infinite`;
                marquee.style.transform = 'translateX(100%)';

                // 动态生成 keyframes
                var styleSheet = document.createElement("style");
                styleSheet.type = "text/css";
                styleSheet.innerText = `
                  @keyframes notice-marquee-scroll {
                  0% { transform: translateX(${wrapperWidth}px);}
                  100% { transform: translateX(-${contentWidth}px);}
                  }`;
                document.head.appendChild(styleSheet);
              }
            });
          </script>
        {% endif %}

        <!-- 内容区 -->
        {% block left_side %}
        {% endblock left_side %}

        <!-- 边栏 -->
        {% block right_side %}
        {% endblock right_side %}

      </div>

      <!-- 内容扩展 -->
      {% block extra_content %}
      {% endblock extra_content %}

    </main>

    <!-- 页脚部分 -->
    <footer>
      <div>
        <p>
          本站所有插画均来自网络公开资源，图片版权归原作者所有
          <br />
          所有图片仅限于小范围内传播、学习使用，并请在下载后24小时内删除。
          <br />
          如果有侵权请联系管理员删除：<a href="mailto:{{ cfg.ADMIN_EMAIL }}">{{ cfg.ADMIN_EMAIL }}</a>，敬请谅解!。
          <br />
          <a href="/w/privacy/">隐私政策</a> © 2024 次元画册.
        </p>
      </div>
    </footer>

    <script type="text/javascript"
            src="https://cdn.bootcdn.net/ajax/libs/notyf/3.10.0/notyf.min.js"></script>
    <script type="text/javascript"
            src="{{ static('js/pure-menus.js') }}"></script>
    <script type="text/javascript"
            src="{{ static('js/functions.js') }}"></script>
    <script type="text/javascript">
      // 初始化通知对象
      var notyf = new Notyf({
        duration: 10000,
        position: { x: 'right', y: 'top' },
        ripple: false,
      });

      document.addEventListener('DOMContentLoaded', function() {
        // 导航标签高亮
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.pure-menu-item');

        menuItems.forEach((item) => {
          const link = item.querySelector('a');
          if (link && currentPath.startsWith(link.getAttribute('href'))) {
            item.classList.add('pure-menu-selected');
            item.classList.add('b-500');
          }
        });

        // 图片错误处理
        const images = document.getElementsByTagName('img');
        for (let img of images) {
          // 只检查已完成加载且宽度为0的图片
          if (img.complete && img.naturalWidth === 0) {
            img.src = "{{ static('img/no_img.svg') }}";
            img.style.minWidth = '20px';
            img.style.minHeight = '20px';
          }

          img.onerror = function() {
            console.error('Image error occurred:', this.src);
            this.src = "{{ static('img/no_img.svg') }}";
            this.style.minWidth = '20px';
            this.style.minHeight = '20px';
          };
        }

        // 处理导航栏滚动
        let lastScrollY = window.scrollY;
        const header = document.querySelector('header');
        const mobileNav = document.getElementById('mobileNav');

        window.addEventListener('scroll', () => {
          const currentScrollY = window.scrollY;

          if (currentScrollY > lastScrollY) {
            // 向下滚动页面时，隐藏上、下导航栏
            mobileNav.style.transform = 'translateY(100%)';
          } else if (currentScrollY < lastScrollY) {
            // 向上滚动页面时，显示上、下导航栏
            mobileNav.style.transform = 'translateY(0)';
          }

          lastScrollY = currentScrollY;
        });

        // 高亮当前页面对应的底部导航项
        const mobileNavItems = document.querySelectorAll('.mobile-nav .nav-item');
        mobileNavItems.forEach(item => {
          if (currentPath.startsWith(item.getAttribute('href'))) {
            item.classList.add('active');
          }
        });

        // 消息提示
        noticeFromURL();

        // 初始化热门搜索功能
        initHotSearch();
      });
    </script>
    <!-- JS 扩展 -->
    {% block extra_js %}
    {% endblock extra_js %}

  </body>
</html>
