{% extends "base.html" %}

{% block left_side %}
  <h1>
    <i class="fa-solid fa-magnifying-glass mgr-2-5"></i>
    「 <span class="fg-blue">{{ kw }}</span> 」
  </h1>

  <div class="pure-menu pure-menu-horizontal">
    <ul class="pure-menu-list tab-menu">
      <li class="pure-menu-item
                 {% if stype == 'illust' %}pure-menu-selected{% endif %}">
        <a href="/w/search/?kw={{ kw }}&stype=illust"
           class="pure-menu-link">
          <i class="fa-solid fa-image mgr-2-5"></i>插画
        </a>
      </li>
      <li class="pure-menu-item
                 {% if stype == 'artist' %}pure-menu-selected{% endif %}">
        <a href="/w/search/?kw={{ kw }}&stype=artist"
           class="pure-menu-link">
          <i class="fa-solid fa-palette mgr-2-5"></i>画师
        </a>
      </li>
      <li class="pure-menu-item
                 {% if stype == 'album' %}pure-menu-selected{% endif %}">
        <a href="/w/search/?kw={{ kw }}&stype=album"
           class="pure-menu-link">
          <i class="fa-regular fa-images mgr-2-5"></i>专辑
        </a>
      </li>
    </ul>
  </div>

  <div>
    <h5 class="mgy-1-5">热门搜索</h5>
    <div class="pure-g xs">
      {% for word in hot_words %}
        <div class="pd-1-5">
          <a class="tag reflect ellipsis pure-u-1 bg-{{ loop.cycle('blue', 'dustypurple', 'lightgreen', 'lightblue', 'brown', 'cyan', 'lightpink', 'golden', 'purple') }}"
             href="/w/search/?kw={{ word }}&stype={{ stype }}">{{ word }}</a>
        </div>
      {% endfor %}
    </div>
  </div>
  <hr />

  {% if stype == 'illust' %}
    {% with illusts=items, enable_filter= True %}
      {% include 'i_illusts.html' %}
    {% endwith %}

  {% elif stype == 'artist' %}
    {% with artists=items %}
      {% include 'i_artists.html' %}
    {% endwith %}

  {% elif stype == 'album' %}
    {% with albums=items %}
      {% include 'i_albums.html' %}
    {% endwith %}

  {% else %}
    {% include "no_more.html" %}
  {% endif %}

{% endblock %}
