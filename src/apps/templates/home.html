{% extends 'base.html' %}

{% block seo %}
  <title>次元画册｜免费下载4K游戏动漫壁纸及Cosplay资源</title>
  <meta name="description"
        content="「次元画册」专注分享全网人气画师作品及Cosplay资源。免费下载4K游戏美图、动漫壁纸。二次元爱好者必藏！" />
  <meta name="keywords" content="游戏插画, 动漫壁纸, Cosplay资源, 免费下载" />
  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "次元画册｜免费下载4K游戏动漫壁纸及Cosplay资源",
      "description": "「次元画册」专注分享全网人气画师作品及Cosplay资源。免费下载4K游戏美图、动漫壁纸。二次元爱好者必藏！",
      "url": "https://www.{{cfg.DOMAIN}}/",
      "keywords": "游戏插画, 动漫壁纸, Cosplay资源, 免费下载",
      "publisher": {
        "@type": "Organization",
        "name": "次元画册",
        "url": "https://www.{{cfg.DOMAIN}}",
        "logo": {
          "@type": "ImageObject",
          "url": "{{ static('img/favicon.svg') }}",
          "width": 512,
          "height": 512
        }
      },
      "mainEntity": [{
        "@type": "CollectionPage",
        "name": "插画精选",
        "url": "https://www.{{cfg.DOMAIN}}/w/illust/",
        "description": "精选热门游戏和动漫的美图壁纸，每张都有4K画质。资源库已累计收录10万多张美图！"
      }, {
        "@type": "CollectionPage",
        "name": "画师推荐",
        "url": "https://www.{{cfg.DOMAIN}}/artist/",
        "description": "推荐全网人气画师及其代表作，探索他们的艺术世界。"
      }, {
        "@type": "CollectionPage",
        "name": "专辑推荐",
        "url": "https://www.{{cfg.DOMAIN}}/album/",
        "description": "精心整理热门游戏、动漫的美图合集，以及亮眼的 Cosplay 作品，展现角色扮演的魅力。"
      }]
    }
  </script>
{% endblock seo %}

{% block left_side %}
  <div class="pure-g">
    <div class=" pure-u-1 pure-u-sm-2-5 pd-2-5">
      <div class="swiper-wrapper">
        <div class="swiper">
          {% for tag in tags[2:3] %}
            <div class="swiper-item {% if loop.first %}active{% endif %}">
              <a href="/w/tag/{{ tag.id }}">
                <img src="{{ static('small/', tag.cover) }}" alt="cover" />
                <span class="ellipsis">
                  <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag.cname }}
                </span>
              </a>
            </div>
          {% endfor %}
        </div>
        {%- if False -%}
          <div class="dots">
            {% for tag in tags[2:3] %}
              <span class="dot {% if loop.first %}active{% endif %}"></span>
            {% endfor %}
          </div>
        {%- endif -%}
      </div>
    </div>
    <div class="pure-u-1 pure-u-sm-3-5">
      <div class="pure-g">
        {% for tag in tags[3:9] %}
          <div class="pure-u-1-2 pure-u-sm-1-3 pd-2-5">
            <div class="tag-cover">
              <a href="/w/tag/{{ tag.id }}">
                <img src="{{ static('small/', tag.cover) }}" alt="cover" />
                <span class="ellipsis">
                  <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag.cname }}
                </span>
              </a>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
  <div id="content" class="pure-u-1 pure-u-sm-4-5">
    <!-- AD: Static Banner -->
    {% include "ad_static_banner_normal.html" %}

    <!-- 热门标签 -->
    <div id="hotTags" class="pure-g">
      <h3 class="pure-u-1">人气标签</h3>
      {% for tag in tags[-12:] %}
        <div class="pure-u-1-3 pure-u-sm-1-4 pure-u-md-1-6 pd-1-5 ">
          <a class="tag reflect ellipsis pure-u-1 bg-{{ loop.cycle('blue', 'dustypurple', 'lightgreen', 'lightblue', 'brown', 'cyan', 'lightpink', 'golden', 'purple') }}"
             href="/w/tag/{{ tag.id }}">
            <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag.cname }}
          </a>
        </div>
      {% endfor %}
      <div class="pure-u-1 t-right mg-1">
        <a href="/w/tag/">查看更多 <i class="fa-solid fa-angles-right"></i></a>
      </div>
    </div>

    <!-- 热门专辑 -->
    <div id="hotAlbums" class="pure-g">
      <h3 class="pure-u-1">专辑推荐</h3>
      {% for alb in albums %}
        <div class="pure-u-1 pure-u-sm-1-2 pure-u-md-1-3">
          <div class="grid-container sm">
            <a href="/w/album/{{ alb.id }}">
              <div class="thumb-container reflect">
                <!-- 缩略图 -->
                <img class="thumb-album"
                     src="{{ alb.thumbnail }}"
                     alt="alb-{{ alb.id }}" />
              </div>
              <div class="thumb-intro">
                <!-- 标题 -->
                <h4 class="mg-0 ellipsis">
                  <i class="fa-solid fa-circle-info mgr-2-5"></i>{{ alb.title }}
                </h4>
                <span class="hor-between ellipsis">
                  <strong class="{%- if alb.catg == 'Cosplay' -%}fg-pink{%- else -%}fg-blue{%- endif -%}">
                    <i class="fa-solid fa-tag mgr-2-5"></i>{{ alb.catg }}
                  </strong>
                </span>
              </div>
            </a>
          </div>
        </div>
      {% else %}
        <div class="pure-u-1 mg-1">
          <p class="fg-grey">即将上线，敬请期待。</p>
        </div>
      {% endfor %}
      <div class="pure-u-1 t-right mg-1">
        <a href="/w/album/">查看更多 <i class="fa-solid fa-angles-right"></i></a>
      </div>
    </div>
  </div>
{% endblock left_side %}

{% block right_side %}
  <div id="sidebar" class="pure-u-1 pure-u-sm-1-5 pd-3-5">
    <div id="hotArtists" class="pure-g">
      <h3 class="pure-u-1">热门画师</h3>
      {% for artist in artists %}
        <div class="pure-u-1-2 pure-u-sm-1 pdh-1 sm">{% include "i_avatar.html" %}</div>
      {% endfor %}
    </div>
  </div>
{% endblock right_side %}

{% block extra_content %}
  <!-- 最新插画 -->
  <div id="newIllusts" class="pure-g">
    <h3 class="pure-u-1">最新美图</h3>
    {% with disable_dynamic = True %}
      {% include "i_illusts.html" %}
    {% endwith %}

    <div class="pure-u-1 t-right mg-1">
      <a href="/w/illust/">查看更多 <i class="fa-solid fa-angles-right"></i></a>
    </div>
  </div>
{% endblock extra_content %}

{% block extra_js %}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const swiper = document.querySelector('.swiper');
      const items = document.querySelectorAll('.swiper-item');
      const dots = document.querySelectorAll('.dot');
      let currentIndex = 0;
      let intervalId = null;

      if (!swiper || items.length === 0) {
        console.error('Carousel elements not found');
        return;
      }

      function showSlide(index) {
        items.forEach(item => item.classList.remove('active'));
        dots.forEach(dot => dot.classList.remove('active'));

        items[index].classList.add('active');
        dots[index].classList.add('active');
        currentIndex = index;
      }

      function nextSlide() {
        let nextIndex = (currentIndex + 1) % items.length;
        showSlide(nextIndex);
      }

      function startAutoPlay() {
        if (!intervalId) {
          intervalId = setInterval(nextSlide, 3000);
        }
      }

      function stopAutoPlay() {
        if (intervalId) {
          clearInterval(intervalId);
          intervalId = null;
        }
      }

      dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
          showSlide(index);
          stopAutoPlay();
          startAutoPlay();
        });
      });

      swiper.addEventListener('mouseenter', stopAutoPlay);
      swiper.addEventListener('mouseleave', startAutoPlay);


      showSlide(0); // 确保第一张图片显示
      startAutoPlay();
    });
  </script>
{% endblock extra_js %}
