{% extends 'base.html' %}

{% block seo %}
      <title>#{{ tag.fullname }} 插画作品 | 次元画册</title>
      <link rel="canonical"
            href="https://www.{{ cfg.DOMAIN }}/w/tag/{{ tag.id }}" />
      <meta name="description"
            content="探索「{{ tag.fullname }}」标签下的精选二次元插画作品。发现更多相关的游戏、动漫美图和壁纸。" />
      <meta name="keywords"
            content="二次元插画,{{ tag.name }},{{ tag.trans }}" />

      {% block social_meta %}
            <meta property="og:type" content="website" />
            <meta property="og:url"
                  content="https://www.{{ cfg.DOMAIN }}/w/tag/{{ tag.id }}" />
            <meta property="og:title"
                  content="#{{ tag.fullname }} 插画作品 | 次元画册" />
            <meta property="og:description"
                  content="探索「{{ tag.fullname }}」标签下的精选二次元插画作品。发现更多相关的游戏、动漫美图和壁纸。" />
            <meta property="twitter:card" content="summary_large_image" />
            <meta property="twitter:url"
                  content="https://www.{{ cfg.DOMAIN }}/w/tag/{{ tag.id }}" />
            <meta property="twitter:title"
                  content="#{{ tag.fullname }} 插画作品 | 次元画册" />
            <meta property="twitter:description"
                  content="探索「{{ tag.fullname }}」标签下的精选二次元插画作品。发现更多相关的游戏、动漫美图和壁纸。" />
      {% endblock social_meta %}

      <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "ImageGallery",
          "name": "#{{ tag.fullname }} 插画作品 | 次元画册",
          "description": "探索「{{ tag.fullname }}」标签下的精选二次元插画作品。发现更多相关的游戏、动漫美图和壁纸。",
          "url": "https://www.{{ cfg.DOMAIN }}/w/tag/{{ tag.id }}",
          "keywords": "二次元插画,{{ tag.name }},{{ tag.trans }}"
        }
      </script>
{% endblock %}

{% block left_side %}
      <h1>
            <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag.fullname }}
      </h1>
      <p class="w-100 mgb-1">共包含 {{ tag.n_illust }} 个作品</p>
      {% with enable_filter=True %}
            {% include 'i_illusts.html' %}
      {% endwith %}
{% endblock %}
