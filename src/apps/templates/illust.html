{% extends 'base.html' %}

{% block seo %}
  <title>{{ illust.title }} - {{ illust.artist.name }}创作的{{ illust.main_tag }}插画壁纸 | 次元画册</title>
  <link rel="canonical"
        href="https://www.{{ cfg.DOMAIN }}/w/illust/{{ illust.id }}" />
  <meta name="author" content="{{ illust.artist.name }}" />
  <meta name="description"
        content="欣赏由画师 {{ illust.artist.name }} 创作的高清二次元插画作品「{{ illust.title }}」。这张精美的壁纸包含{{ illust.stags }}等标签。立即访问次元画册，下载4K无水印原图。" />
  <meta name="keywords"
        content="二次元少女,动漫插画,游戏壁纸,{{ illust.stags }}" />

  {% block social_meta %}
    <meta property="og:type" content="article" />
    <meta property="og:url"
          content="https://www.{{ cfg.DOMAIN }}/w/illust/{{ illust.id }}" />
    <meta property="og:title"
          content="{{ illust.title }} - {{ illust.artist.name }}创作的{{ illust.main_tag }}插画壁纸" />
    <meta property="og:description"
          content="欣赏由画师 {{ illust.artist.name }} 创作的高清二次元插画作品「{{ illust.title }}」。这张精美的壁纸包含{{ illust.stags }}等标签。" />
    <meta property="og:image" content="{{ illust.large_urls[0] }}" />
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url"
          content="https://www.{{ cfg.DOMAIN }}/w/illust/{{ illust.id }}" />
    <meta property="twitter:title"
          content="{{ illust.title }} - {{ illust.artist.name }}创作的{{ illust.main_tag }}插画壁纸" />
    <meta property="twitter:description"
          content="欣赏由画师 {{ illust.artist.name }} 创作的高清二次元插画作品「{{ illust.title }}」。这张精美的壁纸包含{{ illust.stags }}等标签。" />
    <meta property="twitter:image"
          content="{{ illust.large_urls[0] }}" />
  {% endblock social_meta %}

  <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ImageObject",
      "name": "{{ illust.title }} - {{ illust.artist.name }}创作的{{ illust.main_tag }}插画壁纸",
      "description": "欣赏由画师 {{ illust.artist.name }} 创作的高清二次元插画作品「{{ illust.title }}」。这张精美的壁纸包含{{ illust.stags }}等标签。立即访问次元画册，下载4K无水印原图。",
      "contentUrl": "{{ illust.large_urls[0] }}",
      "creator": {
        "@type": "Person",
        "name": "{{ illust.artist.name }}"
      },
      "author": {
        "@type": "Person",
        "name": "{{ illust.artist.name }}"
      },
      "keywords": "二次元少女,动漫插画,游戏壁纸,{{ illust.stags }}",
      "acquireLicensePage": "https://www.{{ cfg.DOMAIN }}/w/privacy/",
      "license": "https://www.{{ cfg.DOMAIN }}/w/privacy/",
      "copyrightNotice": "次元画册 - 一切权利归原作者所有, 禁止一切商用行为.",
      "creditText": "{{ illust.artist.name }}"
    }
  </script>
{% endblock seo %}

{% block left_side %}
  {% set alt_text = "「" + illust.title + "」- 一张关于" + illust.stags + "的二次元插画，由画师" + illust.artist.name + "创作，适合做手机或电脑壁纸。" %}
  <!-- 内容区: 作品信息 -->
  <div class="pure-u-1 pure-u-md-4-5 content">
    {% if illust.n_page == 1 %}
      <!-- 单图 -->
      <img class="illust"
           src="{{ illust.large_urls[0] }}"
           loading="lazy"
           alt="{{ alt_text }}" />
    {% else %}
      <!-- 首图 -->
      <img class="illust"
           src="{{ illust.large_urls[0] }}"
           loading="lazy"
           alt="{{ alt_text }} (第1张)" />
      <!-- 其余图片 -->
      <div id="hiddenImages" class="hidden">
        {% for url in illust.large_urls[1:] %}
          <img class="illust"
               data-src="{{ url }}"
               loading="lazy"
               alt="{{ alt_text }} (第{{ loop.index + 1 }}张)" />
        {% endfor %}
      </div>
      <!-- 查看全部按钮 -->
      <div class="pure-g">
        <button id="imagesControl"
                class="pure-button rd-semi center fg-white bg-black"
                onclick="show_images()">查看全部</button>
      </div>
    {% endif %}

    <!-- AD: Banner -->
    {% include "ad_static_banner_normal.html" %}

    <!-- 标题 -->
    <div class="pure-g illust-intro">
      <a href="#illustTitle" class="pure-u-1 pure-u-sm-3-5">
        <h1 id="illustTitle">{{ illust.title }}</h1>
      </a>
      <!-- 点赞数和浏览数 -->
      <div class="pure-u-1 pure-u-sm-2-5 illust-stats">
        <span><i class="fa-solid fa-heart fg-red mgr-2-5"></i>{{ illust.n_bookmark }}</span>
        <span><i class="fa-solid fa-eye fg-purple mgr-2-5"></i>{{ illust.n_view }}</span>
        {% if user and user.is_favorited(illust.id) %}
          <span id="favorite-btn-{{ illust.id }}"
                onclick="toggleFavorite('{{ illust.id }}')"
                style="cursor: pointer"><i class="fa-solid fa-star fg-golden mgr-2-5"></i>取消</span>
        {% else %}
          <span id="favorite-btn-{{ illust.id }}"
                onclick="toggleFavorite('{{ illust.id }}')"
                style="cursor: pointer"><i class="fa-regular fa-star mgr-2-5"></i>收藏</span>
        {% endif %}
      </div>

      <p class="w-100">{{ illust.caption | safe }}</p>

      <!-- Pixiv链接、分辨率、发布日期 -->
      <div class="pure-u-1 pd-2-5">
        <i class="fa-brands fa-pixiv mgr-2-5 fg-blue"></i>
        <strong class="w-4">Pixiv ID</strong>：
        <a href="https://www.pixiv.net/artworks/{{ illust.id }}"
           target="_blank">
          <span class="underline">{{ illust.id }}</span>
          <i class="fa-solid fa-up-right-from-square"></i>
        </a>
      </div>
      <div class="pure-u-1 pd-2-5">
        <i class="fa-solid fa-expand mgr-2-5"></i>
        <strong class="w-4">原图大小</strong>：{{ illust.w }} x {{ illust.h }}
      </div>
      <div class="pure-u-1 pd-2-5 fg-gray">
        <i class="fa-solid fa-calendar-days mgr-2-5"></i>
        <strong class="w-4">发布时间</strong>：{{ cn_date(illust.created, "d") }}
      </div>

      <!-- 下载按钮 -->
      <div class="pure-g w-100 hor-around pd-1">
        <button id="downloadHD"
                class="pure-button reflect pd-3-5 mg-1 rd-15 fg-white bg-dazzling xl b-900 btn-shadow vcenter"
                onclick="downloadIllustsHD()">
          <i class="fa-solid fa-circle-down fa-bounce mgr-2-5"></i>
          <div class="price-info">下载 4K 原图</div>
        </button>
        {% if cfg.ENABLE_DOWNLOAD_1K %}
          <button id="download1K"
                  class="pure-button mg-1 rd-15 fg-darkgray xl b-500 btn-shadow vcenter"
                  onclick="downloadIllusts1K()">
            <i class="fa-regular fa-circle-down mgr-2-5"></i>
            <div class="price-info">
              普通画质下载
              <div class="t-center xs b-500">
                <i class="fa-solid fa-coins mgr-2-5"></i> 免费
              </div>
            </div>
          </button>
        {% endif %}
      </div>

      <!-- 标签区 -->
      <div class="pure-g w-100">
        {% if illust.is_ai %}
          <span class="tag mg-2-5 bg-rose b-700"><i class="fa-solid fa-robot mgr-1-5"></i>AI 作品</span>
        {% endif %}

        {% for tag in illust.tags %}
          <a class="tag reflect mg-2-5 bg-{{ loop.cycle('blue', 'dustypurple', 'lightgreen', 'lightblue', 'brown', 'cyan', 'lightpink', 'golden', 'purple') }}"
             href="/w/tag/{{ tag[0] }}"
             title="{{ tag[1] }}"
             rel="tag">
            <i class="fa-solid fa-hashtag mgr-1-5"></i>{{ tag[1] }}
          </a>
        {% endfor %}
      </div>
    </div>
  </div>
{% endblock %}

{% block right_side %}
  <!-- 侧边栏: 画师信息 -->
  <div class="pure-u-1 pure-u-md-1-5 sidebar">
    {% with artist=illust.artist, sz='sm' %}
      {% include 'i_avatar.html' %}
    {% endwith %}
    <!-- 画师最新作品 -->
    <h5>最新作品</h5>
    {% with artist = illust.artist, iid=illust.id %}
      {% include 'i_recent.html' %}
    {% endwith %}

    <!-- AD: Banner -->
    {% include "ad_native_banner_adult.html" %}
  </div>
{% endblock %}

{% block extra_content %}
  <!-- 相关插画 -->
  <div id="relatedIllusts" class="container pure-g">
    <h3 class="w-100">相关插画</h3>
    <div id="loading" class="center pd-5">
      <i class="fa-solid fa-spinner fa-spin-pulse fa-2xl"></i>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script type="text/javascript" src="{{ static('js/download.js') }}"></script>
  <script type="text/javascript">
    // 相关插画加载逻辑
    let loading = false;

    async function loadRelated() {
      if (loading) {
        return;
      }

      loading = true;
      await fetchAPI(
        'GET',
        `/w/irelated/{{ illust.id }}`,
        null,
        (html) => {
          if (html.trim()) {
            let loading = document.getElementById('loading');
            if (loading) loading.remove();
            const container = document.getElementById('relatedIllusts');
            if (container) container.insertAdjacentHTML('beforeend', html);
          }
        }
      );
    }

    // 监听滚动事件
    window.addEventListener('scroll', () => {
      if ((window.innerHeight + window.scrollY) >= document.documentElement.scrollHeight - 300) {
        loadRelated();
      }
    });

    // 显示隐藏图片
    function show_images() {
      const hiddenImages = document.getElementById('hiddenImages');
      hiddenImages.querySelectorAll('img').forEach(img => {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
      });
      hiddenImages.classList.remove('hidden');
      document.getElementById('imagesControl').remove();
    }

    // 下载 1K 图片
    async function downloadIllusts1K() {
      const images = document.querySelectorAll('img.illust');
      const defaultImg = "{{ static('img/no_img.svg') }}";

      const urls = Array.from(images).map(img =>
        (img.src.endsWith(defaultImg) && img.dataset.src) ? img.dataset.src : img.src
      ).filter(url => url && !url.endsWith(defaultImg));

      await download(urls);
    }

    // 下载 HD 图片
    async function downloadIllustsHD() {
      await fetchAPI(
        'GET',
        '/a/illust/download/{{illust.id}}',
        null,
        async (data) => {
          await download(data.hd_urls);
        }
      );
    }

    // 收藏/取消收藏功能
    function toggleFavorite(illustId) {
      const btn = document.getElementById('favorite-btn-' + illustId);
      if (!btn) return;
      const isFavorited = btn.textContent.trim() === '取消';
      const url = isFavorited ? `/a/user/unfavorite/${illustId}` : `/a/user/favorite/${illustId}`;
      fetchAPI('POST', url).then(res => {
        if (!res) return;
        if (res.rc === 0) {
          if (isFavorited) {
            btn.innerHTML = '<i class="fa-regular fa-star mgr-2-5"></i>收藏';
          } else {
            btn.innerHTML = '<i class="fa-solid fa-star fg-golden mgr-2-5"></i>取消';
          }
        } else {
          if (window.notyf) notyf.error(res.msg || '操作失败');
        }
      });
    }
  </script>
{% endblock %}
