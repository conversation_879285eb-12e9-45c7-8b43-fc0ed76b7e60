{% extends 'base.html' %}

<!-- 内容区 -->
{% block left_side %}
  <div class="container">
    <div class="panel mg-center pd-2">
      <!-- 标签页菜单 -->
      <div id="authTab"
           class="pure-menu pure-menu-horizontal t-center">
        <ul class="pure-menu-list tab-menu">
          <li class="pure-menu-item
                     {% if tab != 'register' %}pure-menu-selected{% endif %}">
            <a href="#" class="pure-menu-link lg b-700" data-tab="login">
              <i class="fa-solid fa-right-to-bracket mgr-2-5"></i>登录
            </a>
          </li>
          <li class="pure-menu-item
                     {% if tab == 'register' %}pure-menu-selected{% endif %}">
            <a href="#"
               class="pure-menu-link lg b-700"
               data-tab="register">
              <i class="fa-solid fa-user-plus mgr-2-5"></i>注册
            </a>
          </li>
        </ul>
      </div>

      <!-- 登录表单 -->
      <div id="loginFormContainer"
           {% if tab == 'register' %}class="hidden"{% endif %}>
        <form id="loginForm"
              method="post"
              action="/w/user/login/?backto={{ backto }}"
              class="pure-form pure-form-aligned">
          <fieldset>
            <div class="pure-control-group">
              <label for="loginEmail">
                <i class="fa-regular fa-envelope"></i>
              </label>
              <input type="email"
                     id="loginEmail"
                     name="email"
                     placeholder="请输入邮箱"
                     required />
              <br />
            </div>

            <div class="pure-control-group">
              <label for="loginPassword">
                <i class="fa-solid fa-key"></i>
              </label>
              <input type="password"
                     id="loginPassword"
                     name="password"
                     placeholder="请输入密码"
                     required />
            </div>
            <div class="pure-control-group">
              <label for="loginButton"></label>
              <button type="submit"
                      class="pure-button fg-white bg-blue rd-semi b-500">
                <i class="fa-solid fa-user-check mgr-2-5"></i>登录
              </button>
            </div>
          </fieldset>
        </form>
      </div>

      <!-- 注册表单 -->
      <div id="registerFormContainer"
           {% if tab != 'register' %}class="hidden"{% endif %}>
        <form id="registerForm"
          method="post"
          action="/w/user/register/?backto={{ backto }}""
          class="pure-form pure-form-aligned">
          <fieldset>
            <div class="pure-control-group">
              <label for="registerEmail">
                <i class="fa-regular fa-envelope"></i>
              </label>
              <input type="email"
                     id="registerEmail"
                     name="email"
                     placeholder="请输入邮箱"
                     required />
            </div>

            <div class="pure-control-group">
              <label for="registerVcode">
                <i class="fa-solid fa-shield-halved"></i>
              </label>
              <input type="text"
                     id="registerVcode"
                     name="vcode"
                     placeholder="请输入验证码"
                     required />
            </div>
            <div class="pure-control-group">
              <label for="vcodeButton"></label>
              <button type="button"
                      id="sendVcodeBtn"
                      class="pure-button rd-semi xs fg-white bg-blue">发送验证码</button>
            </div>

            <div class="pure-control-group">
              <label for="registerPassword">
                <i class="fa-solid fa-key"></i>
              </label>
              <input type="password"
                     id="registerPassword"
                     name="password"
                     placeholder="请输入密码"
                     required />
            </div>

            <div class="pure-control-group">
              <label for="registerConfirm">
                <i class="fa-solid fa-check-double"></i>
              </label>
              <input type="password"
                     id="registerConfirm"
                     name="confirm"
                     placeholder="请再次输入密码"
                     required />
              <br />
              <label for="passwordStrength"></label>
              <span class="xs fg-orange">密码长度不能少于 {{ cfg.PWD_MIN_LEN }} 个字符</span>
              <br />
              <label for="passwordStrength"></label>
              <span class="xs fg-orange">且必须包含大写字母和数字</span>
            </div>

            <div class="pure-control-group">
              <label for="registerButton"></label>
              <button type="submit"
                      class="pure-button fg-white bg-blue rd-semi b-500">
                <i class="fa-solid fa-user-plus mgr-2-5"></i>注册
              </button>
            </div>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script type="text/javascript">
    // 切换标签页的函数
    document.getElementById('authTab').querySelectorAll('.pure-menu-link').forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const tab = e.currentTarget.getAttribute('data-tab');
        const loginContainer = document.getElementById('loginFormContainer');
        const registerContainer = document.getElementById('registerFormContainer');
        const loginTab = document.querySelector('[data-tab="login"]').parentElement;
        const registerTab = document.querySelector('[data-tab="register"]').parentElement;

        if (tab === 'login') {
          loginContainer.classList.remove('hidden');
          registerContainer.classList.add('hidden');
          loginTab.classList.add('pure-menu-selected');
          registerTab.classList.remove('pure-menu-selected');
        } else {
          loginContainer.classList.add('hidden');
          registerContainer.classList.remove('hidden');
          loginTab.classList.remove('pure-menu-selected');
          registerTab.classList.add('pure-menu-selected');
        }
      });
    });

    // 登录表单提交
    document.getElementById("loginForm").addEventListener("submit", async function(event) {
      event.preventDefault();

      await fetchAPI(
        'POST',
        this.action, {
          email: document.getElementById('loginEmail').value.trim(),
          password: document.getElementById('loginPassword').value.trim()
        }
      );
    });

    const regEmailInput = document.getElementById('registerEmail');
    const sendButton = document.getElementById('sendVcodeBtn');

    // 发送验证码
    sendButton.addEventListener("click", async function(event) {
      // 检查 Cookie 中是否有上次发送验证码的时间戳
      var nextSendTime = getCookie("nextSendTime");
      var currentTime = new Date().getTime();
      if (nextSendTime && currentTime < nextSendTime) {
        var remainingTime = Math.round((nextSendTime - currentTime) / 1000);
        notyf.error("请等待 " + remainingTime + " 秒后再发送验证码");
        return;
      }

      if (!emailValidation(regEmailInput)) return; // 检查邮箱是否输入正确

      // 调用发送验证码接口
      await fetchAPI(
        'GET',
        '/a/user/vcode/', {
          email: regEmailInput.value.trim()
        },
        (data) => {
          // 禁用按钮
          sendButton.disabled = true;

          // 设置倒计时 120 秒
          var countdown = 120;
          var countdownInterval = setInterval(function() {
            if (countdown <= 0) {
              clearInterval(countdownInterval);
              sendButton.disabled = false;
              sendButton.textContent = '发送验证码';
            } else {
              sendButton.textContent = '重新发送 (' + countdown + ')';
              countdown--;
            }
          }, 1000);

          // 在 Cookie 中记录下次发送验证码的时间戳
          var nextSendTime = new Date().getTime() + 120 * 1000;
          document.cookie = 'nextSendTime=' + nextSendTime + '; path=/';
        }
      );
    });

    const regVcodeInput = document.getElementById('registerVcode');
    const regPasswordInput = document.getElementById("registerPassword");
    const regConfirmInput = document.getElementById("registerConfirm");

    // 焦点离开时，清除密码输入框的验证信息
    regPasswordInput.addEventListener('blur', () => regPasswordInput.setCustomValidity(''));
    regConfirmInput.addEventListener('blur', () => regConfirmInput.setCustomValidity(''));

    // 注册表单提交
    document.getElementById("registerForm").addEventListener("submit", async function(event) {
      event.preventDefault();

      // 检查密码是否正确输入
      if (!chkPassword(regPasswordInput, +"{{ cfg.PWD_MIN_LEN }}")) return;
      if (!chkConfirm(regPasswordInput, regConfirmInput)) return;

      await fetchAPI(
        'POST',
        this.action, {
          email: regEmailInput.value.trim(),
          password: regPasswordInput.value.trim(),
          confirm: regConfirmInput.value.trim(),
          vcode: regVcodeInput.value.trim()
        }
      );
    });
  </script>
{% endblock %}
