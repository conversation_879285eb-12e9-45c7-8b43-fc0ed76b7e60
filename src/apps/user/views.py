from urllib.parse import quote

from fastapi import Request

from apps.user import apis as u_api
from apps.user.forms import LoginForm, ProfileForm, RegisterForm
from apps.user.models import User
from common.decorator import page_hits
from libs.http import redirect, render


async def signup(request: Request, backto='/w/user/profile/'):
    """注册页面"""
    if request.user is not None:
        return redirect('/w/user/profile/', 302)

    if request.method == 'POST':
        form = RegisterForm(**await request.form())  # type: ignore
        user = await u_api.signup(form.email, form.password, form.vcode)
        request.session['uid'] = user.id
        return {'rc': 0, 'msg': '注册成功', 'next_url': backto}

    backto = request.headers.get('referer', backto)
    return render('apps/user_login.html', tab='register', backto=quote(backto))


async def signin(request: Request, notice=False, backto='/w/user/profile/'):
    """登录页面"""
    if request.user is not None:
        return redirect('/w/user/profile/', 302)

    if request.method == 'POST':
        form = LoginForm(**await request.form())  # type: ignore
        user = await u_api.signin(form.email, form.password)
        request.session['uid'] = user.id
        return {'rc': 0, 'msg': '登录成功', 'next_url': backto}

    backto = request.headers.get('referer', backto)
    err = '您还没有登录哦～' if notice else ''
    return render('apps/user_login.html', tab='login', backto=quote(backto), err=err)


async def signout(request: Request):
    """退出登录"""
    await request.session.delete()  # type: ignore
    return redirect('/', 302)


async def profile(request: Request):
    """用户页面"""
    if request.method == 'POST':
        form = ProfileForm(**await request.form())  # type: ignore
        result = await u_api.modify_profile(request.user, form.password)
        return result
    else:
        checkin_log = await u_api.checkin_status(request.user)
        n_checked = len(checkin_log)
        weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        checkin_log = [(wd, i in checkin_log) for i, wd in enumerate(weekdays)]
        return render('apps/user_profile.html', checkin_log=checkin_log, n_checked=n_checked)


async def purchased_illusts(request: Request, page: int = 1, sp: int = 0):
    """已购买的插画"""
    user: User = request.user
    page = page if page > 0 else 1

    illusts, sp = await user.purchased_illusts(page, order='purchased', spos=sp)
    illusts = [await il.overview(with_artist=True) for il in illusts]
    template = 'apps/i_illusts.html' if page > 1 else 'apps/user_illusts.html'
    return render(template, illusts=illusts, sp=sp)


async def purchased_albums(request: Request, page: int = 1):
    """已购买的专辑"""
    user: User = request.user
    page = page if page > 0 else 1
    albums = await user.purchased_albums(page)
    template = 'apps/i_albums.html' if page > 1 else 'apps/user_albums.html'
    return render(template, albums=albums)


async def checkin(request: Request):
    """打卡"""
    return await u_api.checkin(request.user)


async def followed(request: Request, page: int = 1):
    """关注列表"""
    artists = []
    for artist in await request.user.followed_artists(page):
        profile = artist.profile  # type: ignore
        profile['recent'] = [await i.overview() for i in await artist.recent(6)]
        artists.append(profile)

    template = 'apps/i_artists.html' if page > 1 else 'apps/user_followed.html'
    return render(template, artists=artists)


@page_hits('follow', 'aid')
async def follow(request: Request, aid: int):
    """关注"""
    if await request.user.follow(aid):
        return {'rc': 0, 'msg': '关注成功'}
    else:
        return {'rc': 0, 'msg': '画师已在关注列表'}


@page_hits('follow', 'aid', score=-1)
async def unfollow(request: Request, aid: int):
    """取消关注"""
    if await request.user.unfollow(aid):
        return {'rc': 0, 'msg': '已取消关注'}
    else:
        return {'rc': 0, 'msg': '画师不在关注列表'}


async def favorited(request: Request, page: int = 1):
    """收藏的插画"""
    page = page if page > 0 else 1

    illusts, sp = await request.user.favorited_illusts(page)
    illusts = [await i.overview(with_artist=True) for i in illusts]
    template = 'apps/i_illusts.html' if page > 1 else 'apps/user_favorited.html'
    return render(template, illusts=illusts, sp=sp)


@page_hits('favorite', 'iid')
async def favorite(request: Request, iid: int):
    """收藏插画"""
    if await request.user.favorite(iid):
        return {'rc': 0, 'msg': '收藏成功'}
    else:
        return {'rc': 0, 'msg': '已在收藏列表'}


@page_hits('favorite', 'iid', score=-1)
async def unfavorite(request: Request, iid: int):
    """取消收藏插画"""
    if await request.user.unfavorite(iid):
        return {'rc': 0, 'msg': '已取消收藏'}
    else:
        return {'rc': 0, 'msg': '已不在收藏列表'}
